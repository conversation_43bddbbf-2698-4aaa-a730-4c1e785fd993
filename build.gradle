// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias libs.plugins.android.application apply false
    alias libs.plugins.android.library apply false
    alias libs.plugins.kotlin.android apply false
    alias libs.plugins.hilt apply false
    alias libs.plugins.google.services apply false
    alias libs.plugins.firebase.crashlytics apply false
    alias libs.plugins.firebase.perf apply false
    alias libs.plugins.ksp apply false
    alias libs.plugins.mokkery apply false
    alias(libs.plugins.jetbrains.kotlin.jvm) apply false
}
allprojects {
    repositories {
        maven {
            url 'https://maven.zohodl.com'
        }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven {url 'https://dl.google.com/dl/android/maven2/'}
        maven {url 'https://www.jitpack.io'}
        maven {url 'https://jitpack.io' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}