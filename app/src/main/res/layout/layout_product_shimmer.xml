<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/_4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/_16dp">

        <View
            android:id="@+id/vw_shimmer_header_1"
            android:layout_width="150dp"
            android:layout_height="@dimen/_10dp"
            android:background="@drawable/shimmer_grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vw_shimmer_header_2"
            android:layout_width="120dp"
            android:layout_height="@dimen/_10dp"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@drawable/shimmer_grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_shimmer_header_1" />

        <View
            android:id="@+id/vw_shimmer_button"
            android:layout_width="60dp"
            android:layout_height="@dimen/_30dp"
            android:background="@drawable/shimmer_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>