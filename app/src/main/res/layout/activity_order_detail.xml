<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_payment_history_detail"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_5">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_view"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_0dp"
        app:layout_constraintBottom_toTopOf="@id/cl_buttons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="?attr/actionBarSize">

            <View
                android:id="@+id/vw_top_background"
                android:layout_width="@dimen/_0dp"
                android:layout_height="150dp"
                android:background="@drawable/payment_tab_gradient_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderStatusView
                android:id="@+id/order_status_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderInfoView
                android:id="@+id/order_info_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_status_view"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderInfoMessageView
                android:id="@+id/order_info_message_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_info_view"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderHelpView
                android:id="@+id/order_help_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_20dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/order_info_message_view"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderInvoiceView
                android:id="@+id/order_invoice_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_help_view"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderStatusInfoView
                android:id="@+id/order_status_info_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_20dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/order_invoice_view"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderPaymentInfoView
                android:id="@+id/order_payment_info_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_status_info_view"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.OrderPpobInfoView
                android:id="@+id/order_ppob_info_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_payment_info_view"
                tools:visibility="visible" />

            <com.bukuwarung.edc.payments.ui.widgets.PoweredByFooterView
                android:id="@+id/powered_by_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/order_ppob_info_view"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_complete_payment"
            style="@style/ButtonFill.Yellow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/make_payment"
            android:textAllCaps="false"
            app:cornerRadius="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:rippleColor="@color/black_40" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/pb_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>