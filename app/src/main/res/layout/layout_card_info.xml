<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/tb_card_info"
        layout="@layout/widget_toolbar"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_card"
        android:layout_width="@dimen/_0dp"
        android:layout_height="140dp"
        android:layout_marginHorizontal="80dp"
        android:layout_marginTop="@dimen/_24dp"
        android:src="@drawable/ic_card"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb_card_info" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_info_title"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_24dp"
        android:text="@string/card_information"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_card" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_info_txt"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/check_card_details"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_info_title" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_card_title"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_24dp"
        android:text="@string/card_number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_info_txt" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_card_num"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_card_title"
        tools:text="6221 **** **** 1537" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_expiry_title"
        style="@style/Body2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/valid_until"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_card_num" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_expiry_value"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_expiry_title"
        tools:text="6221 **** **** 1537" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit"
        style="@style/ButtonFill.Yellow"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:text="@string/next"
        android:textAllCaps="false"
        app:cornerRadius="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>