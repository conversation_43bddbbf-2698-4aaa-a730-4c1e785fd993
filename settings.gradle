import org.gradle.api.initialization.resolve.RepositoriesMode

pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://maven.zohodl.com'
        }
        maven {url 'https://jitpack.io'}
        maven { url "https://packages.bureau.id/api/packages/Bureau/maven" }
    }
}
rootProject.name = "EDC"

// Function to recursively include all Gradle projects in the core directory
def includeDirectory(String dirName) {
    def dir = new File(rootDir, dirName)
    if (dir.exists() && dir.isDirectory()) {
        scanDirectoryForProjects(dir, dirName)
    } else {
        println "$dirName directory not found at: ${coreDir.absolutePath}"
    }
}

// Recursive helper function to scan directories for Gradle projects
def scanDirectoryForProjects(File directory, String pathPrefix) {
    directory.listFiles()?.each { file ->
        if (file.isDirectory()) {
            def buildFile = new File(file, 'build.gradle')
            def buildFileKts = new File(file, 'build.gradle.kts')

            // Check if it's a valid Gradle project (has build.gradle or build.gradle.kts)
            if (buildFile.exists() || buildFileKts.exists()) {
                def projectName = ":${pathPrefix}:${file.name}"
                include projectName
                println "Included project: $projectName"
            }

            // Recursively scan subdirectories regardless of whether current dir is a project
            scanDirectoryForProjects(file, "${pathPrefix}:${file.name}")
        }
    }
}
enableFeaturePreview 'TYPESAFE_PROJECT_ACCESSORS'

include ':app'
include ':ui-component'
include ':bluetooth-devices-setup'
includeDirectory("core")